/**
 * Test Helper Utilities
 * 
 * Provides common testing utilities and custom Jest matchers
 */

import { PoolClient } from 'pg';
import { DatabaseTestManager } from './database-test-manager';

/**
 * Test isolation decorator - wraps test in a transaction that gets rolled back
 */
export function withTestTransaction(testName?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const testId = testName || `${target.constructor.name}.${propertyName}`;
      const dbManager = (global as any).dbTestManager as DatabaseTestManager;
      
      if (!dbManager) {
        throw new Error('Database test manager not available. Ensure test setup is properly configured.');
      }

      return await dbManager.withTestTransaction(testId, async (client: PoolClient) => {
        // Make client available to the test
        (this as any).testClient = client;
        return await originalMethod.apply(this, args);
      });
    };

    return descriptor;
  };
}

/**
 * Database assertion helpers
 */
export class DatabaseAssertions {
  constructor(private client: PoolClient) {}

  /**
   * Assert that a table has a specific number of rows
   */
  async assertRowCount(tableName: string, expectedCount: number): Promise<void> {
    const result = await this.client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
    const actualCount = parseInt(result.rows[0].count, 10);
    
    if (actualCount !== expectedCount) {
      throw new Error(`Expected ${tableName} to have ${expectedCount} rows, but found ${actualCount}`);
    }
  }

  /**
   * Assert that a record exists with specific conditions
   */
  async assertRecordExists(tableName: string, conditions: Record<string, any>): Promise<void> {
    const whereClause = Object.keys(conditions)
      .map((key, index) => `${key} = $${index + 1}`)
      .join(' AND ');
    
    const values = Object.values(conditions);
    const result = await this.client.query(
      `SELECT COUNT(*) as count FROM ${tableName} WHERE ${whereClause}`,
      values
    );
    
    const count = parseInt(result.rows[0].count, 10);
    if (count === 0) {
      throw new Error(`Expected record to exist in ${tableName} with conditions: ${JSON.stringify(conditions)}`);
    }
  }

  /**
   * Assert that a record does not exist with specific conditions
   */
  async assertRecordNotExists(tableName: string, conditions: Record<string, any>): Promise<void> {
    const whereClause = Object.keys(conditions)
      .map((key, index) => `${key} = $${index + 1}`)
      .join(' AND ');
    
    const values = Object.values(conditions);
    const result = await this.client.query(
      `SELECT COUNT(*) as count FROM ${tableName} WHERE ${whereClause}`,
      values
    );
    
    const count = parseInt(result.rows[0].count, 10);
    if (count > 0) {
      throw new Error(`Expected no record to exist in ${tableName} with conditions: ${JSON.stringify(conditions)}`);
    }
  }

  /**
   * Get a record by ID
   */
  async getRecord(tableName: string, id: string): Promise<any> {
    const result = await this.client.query(`SELECT * FROM ${tableName} WHERE id = $1`, [id]);
    return result.rows[0] || null;
  }

  /**
   * Assert that a column has a specific value
   */
  async assertColumnValue(
    tableName: string,
    id: string,
    columnName: string,
    expectedValue: any
  ): Promise<void> {
    const result = await this.client.query(
      `SELECT ${columnName} FROM ${tableName} WHERE id = $1`,
      [id]
    );
    
    if (result.rows.length === 0) {
      throw new Error(`Record with id ${id} not found in ${tableName}`);
    }
    
    const actualValue = result.rows[0][columnName];
    if (actualValue !== expectedValue) {
      throw new Error(
        `Expected ${columnName} to be ${expectedValue}, but found ${actualValue}`
      );
    }
  }
}

/**
 * Performance testing utilities
 */
export class PerformanceTestUtils {
  /**
   * Measure execution time of an async function
   */
  static async measureTime<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const start = process.hrtime.bigint();
    const result = await fn();
    const end = process.hrtime.bigint();
    const duration = Number(end - start) / 1000000; // Convert to milliseconds
    
    return { result, duration };
  }

  /**
   * Run a function multiple times and get statistics
   */
  static async benchmark<T>(
    fn: () => Promise<T>,
    iterations = 10
  ): Promise<{
    results: T[];
    durations: number[];
    avgDuration: number;
    minDuration: number;
    maxDuration: number;
  }> {
    const results: T[] = [];
    const durations: number[] = [];

    for (let i = 0; i < iterations; i++) {
      const { result, duration } = await this.measureTime(fn);
      results.push(result);
      durations.push(duration);
    }

    const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    const minDuration = Math.min(...durations);
    const maxDuration = Math.max(...durations);

    return {
      results,
      durations,
      avgDuration,
      minDuration,
      maxDuration,
    };
  }
}

/**
 * Custom Jest matchers
 */
export function setupCustomMatchers(): void {
  expect.extend({
    toBeValidUUID(received: string) {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      const pass = typeof received === 'string' && uuidRegex.test(received);
      
      return {
        message: () => `expected ${received} to be a valid UUID`,
        pass,
      };
    },

    toBeValidDate(received: any) {
      const pass = received instanceof Date && !isNaN(received.getTime());
      
      return {
        message: () => `expected ${received} to be a valid Date`,
        pass,
      };
    },

    toHaveValidSchema(received: any) {
      const pass = typeof received === 'object' && 
                   received !== null && 
                   typeof received.id === 'string' &&
                   received.createdAt instanceof Date &&
                   received.updatedAt instanceof Date;
      
      return {
        message: () => `expected object to have valid schema with id, createdAt, and updatedAt`,
        pass,
      };
    },
  });
}

/**
 * Test data validation utilities
 */
export class TestValidationUtils {
  /**
   * Validate that an object has required properties
   */
  static validateRequiredProperties(obj: any, requiredProps: string[]): void {
    const missing = requiredProps.filter(prop => !(prop in obj));
    if (missing.length > 0) {
      throw new Error(`Missing required properties: ${missing.join(', ')}`);
    }
  }

  /**
   * Validate that an object has the correct types for properties
   */
  static validatePropertyTypes(obj: any, typeMap: Record<string, string>): void {
    for (const [prop, expectedType] of Object.entries(typeMap)) {
      if (prop in obj) {
        const actualType = typeof obj[prop];
        if (actualType !== expectedType) {
          throw new Error(`Property ${prop} should be ${expectedType}, but is ${actualType}`);
        }
      }
    }
  }

  /**
   * Validate that a value is within a range
   */
  static validateRange(value: number, min: number, max: number, propertyName = 'value'): void {
    if (value < min || value > max) {
      throw new Error(`${propertyName} ${value} is not within range [${min}, ${max}]`);
    }
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error(`Invalid email format: ${email}`);
    }
  }
}

/**
 * Async test utilities
 */
export class AsyncTestUtils {
  /**
   * Wait for a condition to be true
   */
  static async waitFor(
    condition: () => Promise<boolean> | boolean,
    timeout = 5000,
    interval = 100
  ): Promise<void> {
    const start = Date.now();
    
    while (Date.now() - start < timeout) {
      if (await condition()) {
        return;
      }
      await this.delay(interval);
    }
    
    throw new Error(`Condition not met within ${timeout}ms`);
  }

  /**
   * Delay execution
   */
  static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Run multiple async operations concurrently
   */
  static async concurrent<T>(operations: (() => Promise<T>)[]): Promise<T[]> {
    return Promise.all(operations.map(op => op()));
  }
}

// Initialize custom matchers
setupCustomMatchers();
