/**
 * PostgreSQL connection management with enhanced pooling and transaction support
 */

import { Pool, PoolClient, PoolConfig, QueryResult } from 'pg';
import { DatabaseConfig } from '../config/types';
import { EventEmitter } from 'events';

export interface ConnectionStats {
  totalConnections: number;
  idleConnections: number;
  waitingClients: number;
  maxConnections: number;
}

export interface TransactionOptions {
  isolationLevel?: 'READ UNCOMMITTED' | 'READ COMMITTED' | 'REPEATABLE READ' | 'SERIALIZABLE';
  readOnly?: boolean;
  deferrable?: boolean;
}

export class DatabaseConnection extends EventEmitter {
  private pool: Pool | null = null;
  private config: DatabaseConfig;
  private isConnected = false;
  private connectionAttempts = 0;
  private maxConnectionAttempts = 5;

  constructor(config: DatabaseConfig) {
    super();
    this.config = config;
  }

  async connect(): Promise<void> {
    if (this.pool && this.isConnected) {
      return;
    }

    this.connectionAttempts++;

    const poolConfig: PoolConfig = {
      host: this.config.host,
      port: this.config.port,
      database: this.config.database,
      user: this.config.username,
      password: this.config.password,
      ssl: this.config.ssl,
      max: this.config.poolSize || 10,
      connectionTimeoutMillis: this.config.connectionTimeout || 30000,
      idleTimeoutMillis: this.config.idleTimeout || 30000,
      maxUses: this.config.maxUses || 7500,
      allowExitOnIdle: this.config.allowExitOnIdle || false,
      maxLifetimeSeconds: this.config.maxLifetimeSeconds || 0,
      statement_timeout: this.config.statementTimeout || 0,
      query_timeout: this.config.queryTimeout || 0,
      keepalive: this.config.keepAlive || true,
      keepalives_idle: this.config.keepAliveInitialDelayMillis || 0,
      application_name: this.config.application_name || 'erp-ts',
    };

    try {
      this.pool = new Pool(poolConfig);

      // Set up pool event handlers
      this.setupPoolEventHandlers();

      // Test connection
      const client = await this.pool.connect();
      try {
        await client.query('SELECT NOW()');
        this.isConnected = true;
        this.connectionAttempts = 0;
        console.log('✅ Database connection established');
        this.emit('connected');
      } finally {
        client.release();
      }
    } catch (error) {
      this.isConnected = false;
      console.error(`❌ Database connection failed (attempt ${this.connectionAttempts}):`, error);
      this.emit('error', error);

      if (this.connectionAttempts < this.maxConnectionAttempts) {
        console.log(`Retrying connection in 5 seconds...`);
        await this.delay(5000);
        return this.connect();
      }

      throw error;
    }
  }

  private setupPoolEventHandlers(): void {
    if (!this.pool) return;

    this.pool.on('error', (err) => {
      console.error('Database pool error:', err);
      this.isConnected = false;
      this.emit('error', err);
    });

    this.pool.on('connect', (client) => {
      console.log('New client connected to database pool');
      this.emit('clientConnected', client);
    });

    this.pool.on('acquire', (client) => {
      console.log('Client acquired from pool');
      this.emit('clientAcquired', client);
    });

    this.pool.on('remove', (client) => {
      console.log('Client removed from pool');
      this.emit('clientRemoved', client);
    });
  }

  async disconnect(): Promise<void> {
    if (this.pool) {
      try {
        await this.pool.end();
        this.pool = null;
        this.isConnected = false;
        console.log('✅ Database connection closed');
        this.emit('disconnected');
      } catch (error) {
        console.error('Error closing database connection:', error);
        throw error;
      }
    }
  }

  getPool(): Pool {
    if (!this.pool || !this.isConnected) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.pool;
  }

  async getClient(): Promise<PoolClient> {
    return this.getPool().connect();
  }

  getStats(): ConnectionStats {
    const pool = this.getPool();
    return {
      totalConnections: pool.totalCount,
      idleConnections: pool.idleCount,
      waitingClients: pool.waitingCount,
      maxConnections: pool.options.max || 10,
    };
  }

  isHealthy(): boolean {
    return this.isConnected && this.pool !== null;
  }

  async query<T = any>(text: string, params?: any[]): Promise<QueryResult<T>> {
    const pool = this.getPool();
    try {
      const result = await pool.query<T>(text, params);
      return result;
    } catch (error) {
      console.error('Query error:', error);
      throw error;
    }
  }

  async transaction<T>(
    callback: (client: PoolClient) => Promise<T>,
    options?: TransactionOptions
  ): Promise<T> {
    const client = await this.getClient();
    let hasBegun = false;

    try {
      // Start transaction with options
      let beginQuery = 'BEGIN';
      if (options?.isolationLevel) {
        beginQuery += ` ISOLATION LEVEL ${options.isolationLevel}`;
      }
      if (options?.readOnly) {
        beginQuery += ' READ ONLY';
      }
      if (options?.deferrable) {
        beginQuery += ' DEFERRABLE';
      }

      await client.query(beginQuery);
      hasBegun = true;

      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      if (hasBegun) {
        try {
          await client.query('ROLLBACK');
        } catch (rollbackError) {
          console.error('Error during rollback:', rollbackError);
        }
      }
      throw error;
    } finally {
      client.release();
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      await this.query('SELECT 1');
      return true;
    } catch {
      return false;
    }
  }

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    maxRetries = 3,
    retryDelay = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        console.warn(`Operation attempt ${attempt} failed:`, error);

        if (attempt < maxRetries) {
          await this.delay(retryDelay * attempt);
        }
      }
    }

    throw lastError!;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
