/**
 * Main entry point for the ERP TypeScript application
 */

import 'reflect-metadata';

console.log('🚀 ERP TypeScript application starting...');

// Export main modules for library usage
export * from './decorators';
export * from './utils';
// Additional modules will be exported here as they are implemented

async function main(): Promise<void> {
  try {
    console.log('✅ Application initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize application:', error);
    process.exit(1);
  }
}

// Only run main if this file is executed directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}
