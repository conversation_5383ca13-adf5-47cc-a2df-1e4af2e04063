{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/decorators/*": ["src/decorators/*"], "@/models/*": ["src/models/*"], "@/services/*": ["src/services/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "coverage"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node"}}