/**
 * Test Configuration Utilities
 * 
 * Provides configuration management for tests including
 * environment setup and test-specific configurations
 */

import { DatabaseConfig } from '../../src/core/config/types';

export interface TestConfig {
  database: DatabaseConfig;
  timeout: {
    default: number;
    database: number;
    integration: number;
  };
  cleanup: {
    autoCleanup: boolean;
    preserveData: boolean;
  };
  logging: {
    level: 'error' | 'warn' | 'info' | 'debug';
    enableSql: boolean;
  };
}

export class TestConfigManager {
  private static instance: TestConfigManager;
  private config: TestConfig;

  private constructor() {
    this.config = this.loadTestConfig();
  }

  static getInstance(): TestConfigManager {
    if (!TestConfigManager.instance) {
      TestConfigManager.instance = new TestConfigManager();
    }
    return TestConfigManager.instance;
  }

  getConfig(): TestConfig {
    return this.config;
  }

  getDatabaseConfig(): DatabaseConfig {
    return this.config.database;
  }

  private loadTestConfig(): TestConfig {
    return {
      database: {
        host: process.env.TEST_DB_HOST || 'localhost',
        port: parseInt(process.env.TEST_DB_PORT || '5432', 10),
        database: process.env.TEST_DB_NAME || 'erp_test_db',
        username: process.env.TEST_DB_USERNAME || 'postgres',
        password: process.env.TEST_DB_PASSWORD || 'password',
        ssl: process.env.TEST_DB_SSL === 'true',
        poolSize: parseInt(process.env.TEST_DB_POOL_SIZE || '5', 10),
        connectionTimeout: parseInt(process.env.TEST_DB_CONNECTION_TIMEOUT || '10000', 10),
        idleTimeout: parseInt(process.env.TEST_DB_IDLE_TIMEOUT || '10000', 10),
        maxUses: parseInt(process.env.TEST_DB_MAX_USES || '1000', 10),
        allowExitOnIdle: process.env.TEST_DB_ALLOW_EXIT_ON_IDLE === 'true',
        maxLifetimeSeconds: parseInt(process.env.TEST_DB_MAX_LIFETIME_SECONDS || '3600', 10),
        statementTimeout: parseInt(process.env.TEST_DB_STATEMENT_TIMEOUT || '30000', 10),
        queryTimeout: parseInt(process.env.TEST_DB_QUERY_TIMEOUT || '30000', 10),
        keepAlive: process.env.TEST_DB_KEEP_ALIVE !== 'false',
        keepAliveInitialDelayMillis: parseInt(process.env.TEST_DB_KEEP_ALIVE_DELAY || '0', 10),
        application_name: 'erp-ts-test',
      },
      timeout: {
        default: parseInt(process.env.TEST_TIMEOUT_DEFAULT || '10000', 10),
        database: parseInt(process.env.TEST_TIMEOUT_DATABASE || '30000', 10),
        integration: parseInt(process.env.TEST_TIMEOUT_INTEGRATION || '60000', 10),
      },
      cleanup: {
        autoCleanup: process.env.TEST_AUTO_CLEANUP !== 'false',
        preserveData: process.env.TEST_PRESERVE_DATA === 'true',
      },
      logging: {
        level: (process.env.TEST_LOG_LEVEL as any) || 'error',
        enableSql: process.env.TEST_ENABLE_SQL_LOGGING === 'true',
      },
    };
  }

  /**
   * Override configuration for specific tests
   */
  withOverrides(overrides: Partial<TestConfig>): TestConfig {
    return {
      ...this.config,
      ...overrides,
      database: {
        ...this.config.database,
        ...(overrides.database || {}),
      },
      timeout: {
        ...this.config.timeout,
        ...(overrides.timeout || {}),
      },
      cleanup: {
        ...this.config.cleanup,
        ...(overrides.cleanup || {}),
      },
      logging: {
        ...this.config.logging,
        ...(overrides.logging || {}),
      },
    };
  }

  /**
   * Get environment-specific configuration
   */
  getEnvironmentConfig(): Record<string, string> {
    const env = process.env.NODE_ENV || 'test';
    
    const baseConfig = {
      NODE_ENV: env,
      LOG_LEVEL: this.config.logging.level,
      DB_HOST: this.config.database.host,
      DB_PORT: this.config.database.port.toString(),
      DB_DATABASE: this.config.database.database,
      DB_USERNAME: this.config.database.username,
      DB_PASSWORD: this.config.database.password,
    };

    if (this.config.database.ssl) {
      baseConfig['DB_SSL'] = 'true';
    }

    return baseConfig;
  }

  /**
   * Validate test configuration
   */
  validateConfig(): void {
    const errors: string[] = [];

    // Validate database configuration
    if (!this.config.database.host) {
      errors.push('Database host is required');
    }
    if (!this.config.database.database) {
      errors.push('Database name is required');
    }
    if (!this.config.database.username) {
      errors.push('Database username is required');
    }

    // Validate timeouts
    if (this.config.timeout.default <= 0) {
      errors.push('Default timeout must be positive');
    }
    if (this.config.timeout.database <= 0) {
      errors.push('Database timeout must be positive');
    }

    if (errors.length > 0) {
      throw new Error(`Test configuration validation failed:\n${errors.join('\n')}`);
    }
  }

  /**
   * Setup environment variables for tests
   */
  setupEnvironment(): void {
    const envConfig = this.getEnvironmentConfig();
    
    for (const [key, value] of Object.entries(envConfig)) {
      process.env[key] = value;
    }
  }

  /**
   * Check if running in CI environment
   */
  isCI(): boolean {
    return !!(
      process.env.CI ||
      process.env.CONTINUOUS_INTEGRATION ||
      process.env.BUILD_NUMBER ||
      process.env.GITHUB_ACTIONS ||
      process.env.GITLAB_CI
    );
  }

  /**
   * Get test database URL
   */
  getDatabaseUrl(): string {
    const { host, port, database, username, password, ssl } = this.config.database;
    const sslParam = ssl ? '?ssl=true' : '';
    return `postgresql://${username}:${password}@${host}:${port}/${database}${sslParam}`;
  }

  /**
   * Get Jest configuration overrides for tests
   */
  getJestConfig(): Partial<any> {
    return {
      testTimeout: this.config.timeout.default,
      setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
      testEnvironment: 'node',
      collectCoverageFrom: [
        'src/**/*.ts',
        '!src/**/*.d.ts',
        '!src/index.ts',
        '!src/**/*.test.ts',
        '!src/**/*.spec.ts',
      ],
      coverageThreshold: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    };
  }
}

/**
 * Test environment setup utilities
 */
export class TestEnvironment {
  private static configManager = TestConfigManager.getInstance();

  /**
   * Setup test environment
   */
  static setup(): void {
    const config = this.configManager.getConfig();
    
    // Validate configuration
    this.configManager.validateConfig();
    
    // Setup environment variables
    this.configManager.setupEnvironment();
    
    // Configure logging
    if (config.logging.level === 'error') {
      // Suppress console output in tests
      console.log = jest.fn();
      console.info = jest.fn();
      console.warn = jest.fn();
    }
  }

  /**
   * Cleanup test environment
   */
  static cleanup(): void {
    // Restore console methods if they were mocked
    if (jest.isMockFunction(console.log)) {
      (console.log as jest.Mock).mockRestore();
    }
    if (jest.isMockFunction(console.info)) {
      (console.info as jest.Mock).mockRestore();
    }
    if (jest.isMockFunction(console.warn)) {
      (console.warn as jest.Mock).mockRestore();
    }
  }

  /**
   * Get configuration for current environment
   */
  static getConfig(): TestConfig {
    return this.configManager.getConfig();
  }

  /**
   * Check if tests should run with real database
   */
  static shouldUseRealDatabase(): boolean {
    return process.env.TEST_USE_REAL_DB === 'true' || this.configManager.isCI();
  }
}

// Export singleton instance
export const testConfig = TestConfigManager.getInstance();
