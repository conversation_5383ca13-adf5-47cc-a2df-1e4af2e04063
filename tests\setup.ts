import 'reflect-metadata';
import { DatabaseTestManager } from './utils/database-test-manager';
import { TestDataFactory } from './utils/test-data-factory';

// Global test instances
let dbTestManager: DatabaseTestManager;
let testDataFactory: TestDataFactory;

// Global test setup
beforeAll(async () => {
  // Set test environment
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error'; // Reduce log noise in tests

  // Override database config for testing
  process.env.DB_DATABASE = 'erp_test_db';
  process.env.DB_HOST = process.env.TEST_DB_HOST || 'localhost';
  process.env.DB_PORT = process.env.TEST_DB_PORT || '5432';
  process.env.DB_USERNAME = process.env.TEST_DB_USERNAME || 'postgres';
  process.env.DB_PASSWORD = process.env.TEST_DB_PASSWORD || 'password';

  // Initialize test database manager
  dbTestManager = new DatabaseTestManager();
  await dbTestManager.initialize();

  // Initialize test data factory
  testDataFactory = new TestDataFactory(dbTestManager);

  // Make available globally
  (global as any).dbTestManager = dbTestManager;
  (global as any).testDataFactory = testDataFactory;
}, 30000);

afterAll(async () => {
  // Cleanup after all tests
  if (dbTestManager) {
    await dbTestManager.cleanup();
  }
}, 30000);

// Global test utilities
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidUUID(): R;
      toBeValidDate(): R;
      toHaveValidSchema(): R;
    }
  }

  var dbTestManager: DatabaseTestManager;
  var testDataFactory: TestDataFactory;
}
