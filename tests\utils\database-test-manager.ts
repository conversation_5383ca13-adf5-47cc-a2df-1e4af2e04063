/**
 * Database Test Manager
 * 
 * Provides comprehensive database testing utilities including:
 * - Test database setup and teardown
 * - Transaction isolation for tests
 * - Test data cleanup
 * - Schema management for tests
 */

import { Pool, PoolClient } from 'pg';
import { DatabaseConnection } from '../../src/core/database/connection';
import { TransactionManager } from '../../src/core/database/transaction';
import { ConfigReader } from '../../src/core/config/reader';

export interface TestDatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
}

export class DatabaseTestManager {
  private connection: DatabaseConnection | null = null;
  private transactionManager: TransactionManager | null = null;
  private testTransactions = new Map<string, PoolClient>();
  private configReader: ConfigReader;

  constructor() {
    this.configReader = new ConfigReader();
  }

  async initialize(): Promise<void> {
    const config = await this.configReader.read();
    
    // Create test database connection
    this.connection = new DatabaseConnection(config.database);
    await this.connection.connect();
    
    this.transactionManager = new TransactionManager(this.connection);
    
    // Ensure test database is clean
    await this.setupTestDatabase();
    
    console.log('✅ Database test manager initialized');
  }

  async cleanup(): Promise<void> {
    // Clean up any active test transactions
    for (const [testId, client] of this.testTransactions) {
      try {
        await client.query('ROLLBACK');
        client.release();
      } catch (error) {
        console.warn(`Failed to cleanup test transaction ${testId}:`, error);
      }
    }
    this.testTransactions.clear();

    // Close database connection
    if (this.connection) {
      await this.connection.disconnect();
      this.connection = null;
    }
    
    console.log('✅ Database test manager cleaned up');
  }

  /**
   * Start a test transaction that will be automatically rolled back
   */
  async startTestTransaction(testId: string): Promise<PoolClient> {
    if (!this.connection) {
      throw new Error('Database test manager not initialized');
    }

    const client = await this.connection.getClient();
    await client.query('BEGIN');
    
    this.testTransactions.set(testId, client);
    return client;
  }

  /**
   * End a test transaction and rollback changes
   */
  async endTestTransaction(testId: string): Promise<void> {
    const client = this.testTransactions.get(testId);
    if (!client) {
      return;
    }

    try {
      await client.query('ROLLBACK');
      client.release();
    } catch (error) {
      console.warn(`Failed to rollback test transaction ${testId}:`, error);
    } finally {
      this.testTransactions.delete(testId);
    }
  }

  /**
   * Execute a test within an isolated transaction
   */
  async withTestTransaction<T>(
    testId: string,
    callback: (client: PoolClient) => Promise<T>
  ): Promise<T> {
    const client = await this.startTestTransaction(testId);
    
    try {
      return await callback(client);
    } finally {
      await this.endTestTransaction(testId);
    }
  }

  /**
   * Get the database connection for direct access
   */
  getConnection(): DatabaseConnection {
    if (!this.connection) {
      throw new Error('Database test manager not initialized');
    }
    return this.connection;
  }

  /**
   * Get the transaction manager
   */
  getTransactionManager(): TransactionManager {
    if (!this.transactionManager) {
      throw new Error('Database test manager not initialized');
    }
    return this.transactionManager;
  }

  /**
   * Execute a query directly on the test database
   */
  async query<T = any>(text: string, params?: any[]): Promise<T> {
    if (!this.connection) {
      throw new Error('Database test manager not initialized');
    }
    
    const result = await this.connection.query<T>(text, params);
    return result.rows as T;
  }

  /**
   * Truncate all tables (for test cleanup)
   */
  async truncateAllTables(): Promise<void> {
    if (!this.connection) {
      throw new Error('Database test manager not initialized');
    }

    // Get all table names
    const result = await this.connection.query(`
      SELECT tablename 
      FROM pg_tables 
      WHERE schemaname = 'public' 
      AND tablename NOT LIKE 'pg_%'
      AND tablename != 'schema_migrations'
    `);

    if (result.rows.length === 0) {
      return;
    }

    // Truncate all tables
    const tableNames = result.rows.map((row: any) => row.tablename).join(', ');
    await this.connection.query(`TRUNCATE TABLE ${tableNames} RESTART IDENTITY CASCADE`);
  }

  /**
   * Create a test table for testing purposes
   */
  async createTestTable(tableName: string, schema: string): Promise<void> {
    if (!this.connection) {
      throw new Error('Database test manager not initialized');
    }

    await this.connection.query(`
      CREATE TABLE IF NOT EXISTS ${tableName} (
        ${schema}
      )
    `);
  }

  /**
   * Drop a test table
   */
  async dropTestTable(tableName: string): Promise<void> {
    if (!this.connection) {
      throw new Error('Database test manager not initialized');
    }

    await this.connection.query(`DROP TABLE IF EXISTS ${tableName} CASCADE`);
  }

  /**
   * Check if a table exists
   */
  async tableExists(tableName: string): Promise<boolean> {
    if (!this.connection) {
      throw new Error('Database test manager not initialized');
    }

    const result = await this.connection.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = $1
      )
    `, [tableName]);

    return result.rows[0].exists;
  }

  /**
   * Get table row count
   */
  async getTableRowCount(tableName: string): Promise<number> {
    if (!this.connection) {
      throw new Error('Database test manager not initialized');
    }

    const result = await this.connection.query(`SELECT COUNT(*) as count FROM ${tableName}`);
    return parseInt(result.rows[0].count, 10);
  }

  /**
   * Setup test database schema
   */
  private async setupTestDatabase(): Promise<void> {
    if (!this.connection) {
      return;
    }

    // Create schema migrations table if it doesn't exist
    await this.connection.query(`
      CREATE TABLE IF NOT EXISTS schema_migrations (
        version VARCHAR(255) PRIMARY KEY,
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('✅ Test database schema setup complete');
  }

  /**
   * Reset database to clean state
   */
  async resetDatabase(): Promise<void> {
    await this.truncateAllTables();
    console.log('✅ Test database reset complete');
  }

  /**
   * Get database statistics for monitoring
   */
  async getDatabaseStats(): Promise<any> {
    if (!this.connection) {
      throw new Error('Database test manager not initialized');
    }

    const stats = this.connection.getStats();
    const activeTransactions = this.testTransactions.size;

    return {
      ...stats,
      activeTestTransactions: activeTransactions,
      isHealthy: this.connection.isHealthy(),
    };
  }
}
