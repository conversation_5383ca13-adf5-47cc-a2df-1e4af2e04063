/**
 * Advanced Transaction Management System
 * 
 * Provides comprehensive transaction support including:
 * - Nested transactions (savepoints)
 * - Transaction isolation levels
 * - Automatic retry logic
 * - Transaction context tracking
 * - Deadlock detection and recovery
 */

import { PoolClient, QueryResult } from 'pg';
import { DatabaseConnection, TransactionOptions } from './connection';
import { contextManager } from '../context/manager';

export interface SavepointOptions {
  name?: string;
  isolationLevel?: 'READ UNCOMMITTED' | 'READ COMMITTED' | 'REPEATABLE READ' | 'SERIALIZABLE';
}

export interface TransactionContext {
  id: string;
  level: number;
  savepoints: string[];
  startTime: Date;
  isReadOnly: boolean;
  isolationLevel?: string;
}

export class TransactionManager {
  private db: DatabaseConnection;
  private activeTransactions = new Map<string, TransactionContext>();

  constructor(db: DatabaseConnection) {
    this.db = db;
  }

  /**
   * Execute a function within a transaction with automatic rollback on error
   */
  async withTransaction<T>(
    callback: (client: PoolClient, ctx: TransactionContext) => Promise<T>,
    options?: TransactionOptions
  ): Promise<T> {
    const transactionId = this.generateTransactionId();
    const context: TransactionContext = {
      id: transactionId,
      level: 0,
      savepoints: [],
      startTime: new Date(),
      isReadOnly: options?.readOnly || false,
      isolationLevel: options?.isolationLevel,
    };

    this.activeTransactions.set(transactionId, context);

    try {
      return await this.db.transaction(async (client) => {
        return await callback(client, context);
      }, options);
    } finally {
      this.activeTransactions.delete(transactionId);
    }
  }

  /**
   * Execute a function within a nested transaction (savepoint)
   */
  async withSavepoint<T>(
    client: PoolClient,
    ctx: TransactionContext,
    callback: (client: PoolClient, savepointName: string) => Promise<T>,
    options?: SavepointOptions
  ): Promise<T> {
    const savepointName = options?.name || `sp_${ctx.level}_${Date.now()}`;
    ctx.level++;
    ctx.savepoints.push(savepointName);

    try {
      // Create savepoint
      await client.query(`SAVEPOINT ${savepointName}`);

      // Set isolation level if specified
      if (options?.isolationLevel) {
        await client.query(`SET TRANSACTION ISOLATION LEVEL ${options.isolationLevel}`);
      }

      const result = await callback(client, savepointName);
      
      // Release savepoint on success
      await client.query(`RELEASE SAVEPOINT ${savepointName}`);
      
      return result;
    } catch (error) {
      // Rollback to savepoint on error
      try {
        await client.query(`ROLLBACK TO SAVEPOINT ${savepointName}`);
      } catch (rollbackError) {
        console.error(`Failed to rollback to savepoint ${savepointName}:`, rollbackError);
      }
      throw error;
    } finally {
      ctx.level--;
      ctx.savepoints.pop();
    }
  }

  /**
   * Execute with automatic retry on serialization failures
   */
  async withRetryableTransaction<T>(
    callback: (client: PoolClient, ctx: TransactionContext) => Promise<T>,
    options?: TransactionOptions & { maxRetries?: number; retryDelay?: number }
  ): Promise<T> {
    const maxRetries = options?.maxRetries || 3;
    const retryDelay = options?.retryDelay || 100;
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.withTransaction(callback, options);
      } catch (error) {
        lastError = error as Error;
        
        // Check if this is a serialization failure that can be retried
        if (this.isRetryableError(error) && attempt < maxRetries) {
          console.warn(`Transaction attempt ${attempt} failed with retryable error, retrying...`);
          await this.delay(retryDelay * attempt);
          continue;
        }
        
        throw error;
      }
    }

    throw lastError!;
  }

  /**
   * Execute multiple operations atomically
   */
  async atomic<T>(
    operations: Array<(client: PoolClient) => Promise<any>>,
    options?: TransactionOptions
  ): Promise<T[]> {
    return this.withTransaction(async (client) => {
      const results: T[] = [];
      
      for (const operation of operations) {
        const result = await operation(client);
        results.push(result);
      }
      
      return results;
    }, options);
  }

  /**
   * Execute a batch of queries atomically
   */
  async batch<T = any>(
    queries: Array<{ text: string; params?: any[] }>,
    options?: TransactionOptions
  ): Promise<QueryResult<T>[]> {
    return this.withTransaction(async (client) => {
      const results: QueryResult<T>[] = [];
      
      for (const query of queries) {
        const result = await client.query<T>(query.text, query.params);
        results.push(result);
      }
      
      return results;
    }, options);
  }

  /**
   * Get active transaction context
   */
  getTransactionContext(transactionId: string): TransactionContext | undefined {
    return this.activeTransactions.get(transactionId);
  }

  /**
   * Get all active transactions
   */
  getActiveTransactions(): TransactionContext[] {
    return Array.from(this.activeTransactions.values());
  }

  /**
   * Check if error is retryable (serialization failure, deadlock, etc.)
   */
  private isRetryableError(error: any): boolean {
    if (!error || typeof error.code !== 'string') {
      return false;
    }

    // PostgreSQL error codes for retryable errors
    const retryableCodes = [
      '40001', // serialization_failure
      '40P01', // deadlock_detected
      '53300', // too_many_connections
      '08006', // connection_failure
      '08001', // sqlclient_unable_to_establish_sqlconnection
    ];

    return retryableCodes.includes(error.code);
  }

  private generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Transaction decorator for automatic transaction management
 */
export function Transactional(options?: TransactionOptions) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const db = this.db || this.database || this.connection;
      if (!db || !db.transaction) {
        throw new Error('No database connection found for @Transactional decorator');
      }

      const txManager = new TransactionManager(db);
      return txManager.withTransaction(async (client, ctx) => {
        // Replace database connection with transaction client for this method call
        const originalDb = this.db || this.database || this.connection;
        this.db = this.database = this.connection = { 
          ...originalDb, 
          query: client.query.bind(client),
          client 
        };

        try {
          return await method.apply(this, args);
        } finally {
          // Restore original database connection
          this.db = this.database = this.connection = originalDb;
        }
      }, options);
    };

    return descriptor;
  };
}

/**
 * Savepoint decorator for nested transaction management
 */
export function Savepoint(options?: SavepointOptions) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const client = this.client;
      const ctx = this.transactionContext;
      
      if (!client || !ctx) {
        throw new Error('@Savepoint decorator can only be used within a transaction');
      }

      const txManager = new TransactionManager(this.db || this.database || this.connection);
      return txManager.withSavepoint(client, ctx, async () => {
        return await method.apply(this, args);
      }, options);
    };

    return descriptor;
  };
}
