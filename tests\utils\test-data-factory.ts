/**
 * Test Data Factory
 * 
 * Provides utilities for creating test data with realistic values
 * and managing test data lifecycle
 */

import { DatabaseTestManager } from './database-test-manager';
import { v4 as uuidv4 } from 'uuid';

export interface TestDataOptions {
  count?: number;
  overrides?: Record<string, any>;
  persist?: boolean;
}

export class TestDataFactory {
  private dbManager: DatabaseTestManager;
  private createdData = new Map<string, any[]>();

  constructor(dbManager: DatabaseTestManager) {
    this.dbManager = dbManager;
  }

  /**
   * Generate a random string
   */
  randomString(length = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Generate a random email
   */
  randomEmail(): string {
    return `${this.randomString(8)}@${this.randomString(6)}.com`;
  }

  /**
   * Generate a random phone number
   */
  randomPhone(): string {
    return `+1${Math.floor(Math.random() * 9000000000) + 1000000000}`;
  }

  /**
   * Generate a random date within a range
   */
  randomDate(start?: Date, end?: Date): Date {
    const startTime = start ? start.getTime() : Date.now() - 365 * 24 * 60 * 60 * 1000; // 1 year ago
    const endTime = end ? end.getTime() : Date.now();
    return new Date(startTime + Math.random() * (endTime - startTime));
  }

  /**
   * Generate a random integer within a range
   */
  randomInt(min = 0, max = 100): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * Generate a random decimal
   */
  randomDecimal(min = 0, max = 1000, decimals = 2): number {
    const value = Math.random() * (max - min) + min;
    return parseFloat(value.toFixed(decimals));
  }

  /**
   * Pick a random item from an array
   */
  randomChoice<T>(items: T[]): T {
    return items[Math.floor(Math.random() * items.length)];
  }

  /**
   * Generate a UUID
   */
  uuid(): string {
    return uuidv4();
  }

  /**
   * Create test user data
   */
  createUserData(overrides: Record<string, any> = {}): any {
    const departments = ['Engineering', 'Sales', 'Marketing', 'HR', 'Finance'];
    const roles = ['admin', 'manager', 'employee', 'readonly'];
    
    return {
      id: this.uuid(),
      username: this.randomString(8),
      email: this.randomEmail(),
      firstName: this.randomChoice(['John', 'Jane', 'Bob', 'Alice', 'Charlie', 'Diana']),
      lastName: this.randomChoice(['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia']),
      department: this.randomChoice(departments),
      role: this.randomChoice(roles),
      isActive: this.randomChoice([true, false]),
      createdAt: this.randomDate(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  /**
   * Create test product data
   */
  createProductData(overrides: Record<string, any> = {}): any {
    const categories = ['Electronics', 'Clothing', 'Books', 'Home', 'Sports'];
    const statuses = ['active', 'inactive', 'discontinued'];
    
    return {
      id: this.uuid(),
      name: `Product ${this.randomString(6)}`,
      description: `Description for ${this.randomString(10)}`,
      category: this.randomChoice(categories),
      price: this.randomDecimal(10, 1000),
      cost: this.randomDecimal(5, 500),
      sku: this.randomString(8).toUpperCase(),
      status: this.randomChoice(statuses),
      stockQuantity: this.randomInt(0, 1000),
      createdAt: this.randomDate(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  /**
   * Create test order data
   */
  createOrderData(overrides: Record<string, any> = {}): any {
    const statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
    
    return {
      id: this.uuid(),
      orderNumber: `ORD-${this.randomString(8).toUpperCase()}`,
      customerId: this.uuid(),
      status: this.randomChoice(statuses),
      totalAmount: this.randomDecimal(50, 5000),
      taxAmount: this.randomDecimal(5, 500),
      shippingAmount: this.randomDecimal(0, 50),
      orderDate: this.randomDate(),
      shippedDate: this.randomDate(),
      deliveredDate: this.randomDate(),
      createdAt: this.randomDate(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  /**
   * Create multiple instances of test data
   */
  createMultiple<T>(
    factory: (overrides?: Record<string, any>) => T,
    count: number,
    overrides: Record<string, any> = {}
  ): T[] {
    const items: T[] = [];
    for (let i = 0; i < count; i++) {
      items.push(factory(overrides));
    }
    return items;
  }

  /**
   * Insert test data into database
   */
  async insertTestData(
    tableName: string,
    data: any[],
    trackForCleanup = true
  ): Promise<any[]> {
    if (data.length === 0) {
      return [];
    }

    const columns = Object.keys(data[0]);
    const placeholders = data.map((_, index) => 
      `(${columns.map((_, colIndex) => `$${index * columns.length + colIndex + 1}`).join(', ')})`
    ).join(', ');

    const values = data.flatMap(item => columns.map(col => item[col]));
    
    const query = `
      INSERT INTO ${tableName} (${columns.join(', ')})
      VALUES ${placeholders}
      RETURNING *
    `;

    const result = await this.dbManager.query(query, values);
    
    if (trackForCleanup) {
      if (!this.createdData.has(tableName)) {
        this.createdData.set(tableName, []);
      }
      this.createdData.get(tableName)!.push(...result);
    }

    return result;
  }

  /**
   * Create and insert test users
   */
  async createUsers(options: TestDataOptions = {}): Promise<any[]> {
    const count = options.count || 5;
    const users = this.createMultiple(
      (overrides) => this.createUserData({ ...options.overrides, ...overrides }),
      count
    );

    if (options.persist !== false) {
      return await this.insertTestData('users', users);
    }

    return users;
  }

  /**
   * Create and insert test products
   */
  async createProducts(options: TestDataOptions = {}): Promise<any[]> {
    const count = options.count || 10;
    const products = this.createMultiple(
      (overrides) => this.createProductData({ ...options.overrides, ...overrides }),
      count
    );

    if (options.persist !== false) {
      return await this.insertTestData('products', products);
    }

    return products;
  }

  /**
   * Create and insert test orders
   */
  async createOrders(options: TestDataOptions = {}): Promise<any[]> {
    const count = options.count || 3;
    const orders = this.createMultiple(
      (overrides) => this.createOrderData({ ...options.overrides, ...overrides }),
      count
    );

    if (options.persist !== false) {
      return await this.insertTestData('orders', orders);
    }

    return orders;
  }

  /**
   * Clean up all created test data
   */
  async cleanup(): Promise<void> {
    for (const [tableName, data] of this.createdData) {
      if (data.length > 0) {
        const ids = data.map(item => item.id);
        await this.dbManager.query(
          `DELETE FROM ${tableName} WHERE id = ANY($1)`,
          [ids]
        );
      }
    }
    this.createdData.clear();
  }

  /**
   * Get created data for a table
   */
  getCreatedData(tableName: string): any[] {
    return this.createdData.get(tableName) || [];
  }

  /**
   * Clear tracking for a specific table
   */
  clearTracking(tableName: string): void {
    this.createdData.delete(tableName);
  }
}
