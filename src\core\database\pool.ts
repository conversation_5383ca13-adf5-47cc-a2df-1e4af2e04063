/**
 * Enhanced Database pool management and health monitoring
 */

import { Pool, PoolClient } from 'pg';
import { DatabaseConnection } from './connection';
import { EventEmitter } from 'events';

export interface PoolStats {
  totalConnections: number;
  idleConnections: number;
  waitingClients: number;
  maxConnections: number;
  healthStatus: 'healthy' | 'degraded' | 'unhealthy';
  lastHealthCheck: Date;
  consecutiveFailures: number;
  uptime: number;
}

export interface HealthCheckResult {
  isHealthy: boolean;
  responseTime: number;
  error?: Error;
  timestamp: Date;
}

export interface PoolMetrics {
  totalQueries: number;
  successfulQueries: number;
  failedQueries: number;
  averageQueryTime: number;
  slowQueries: number;
  connectionErrors: number;
  lastSlowQuery?: {
    query: string;
    duration: number;
    timestamp: Date;
  };
}

export class DatabasePool extends EventEmitter {
  private connection: DatabaseConnection;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private metricsInterval: NodeJS.Timeout | null = null;
  private isHealthy = true;
  private consecutiveFailures = 0;
  private maxConsecutiveFailures = 3;
  private healthCheckHistory: HealthCheckResult[] = [];
  private maxHistorySize = 100;
  private startTime = Date.now();
  private metrics: PoolMetrics = {
    totalQueries: 0,
    successfulQueries: 0,
    failedQueries: 0,
    averageQueryTime: 0,
    slowQueries: 0,
    connectionErrors: 0,
  };
  private slowQueryThreshold = 5000; // 5 seconds

  constructor(connection: DatabaseConnection) {
    super();
    this.connection = connection;
    this.setupConnectionEventHandlers();
  }

  private setupConnectionEventHandlers(): void {
    this.connection.on('error', (error) => {
      this.metrics.connectionErrors++;
      this.emit('connectionError', error);
    });

    this.connection.on('connected', () => {
      this.consecutiveFailures = 0;
      this.emit('connected');
    });

    this.connection.on('disconnected', () => {
      this.emit('disconnected');
    });
  }

  async startHealthMonitoring(intervalMs = 30000): Promise<void> {
    if (this.healthCheckInterval) {
      return;
    }

    // Start health check monitoring
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, intervalMs);

    // Start metrics collection
    this.metricsInterval = setInterval(() => {
      this.collectMetrics();
    }, intervalMs / 2); // Collect metrics more frequently

    // Perform initial health check
    await this.performHealthCheck();

    console.log('✅ Database health monitoring started');
    this.emit('monitoringStarted');
  }

  stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = null;
    }

    console.log('✅ Database health monitoring stopped');
    this.emit('monitoringStopped');
  }

  private async performHealthCheck(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    let result: HealthCheckResult;

    try {
      const isHealthy = await this.connection.healthCheck();
      const responseTime = Date.now() - startTime;

      result = {
        isHealthy,
        responseTime,
        timestamp: new Date(),
      };

      if (isHealthy) {
        this.consecutiveFailures = 0;
        if (!this.isHealthy) {
          this.isHealthy = true;
          console.log('✅ Database health recovered');
          this.emit('healthRecovered', result);
        }
      } else {
        this.consecutiveFailures++;
        if (this.isHealthy && this.consecutiveFailures >= this.maxConsecutiveFailures) {
          this.isHealthy = false;
          console.error('❌ Database health degraded');
          this.emit('healthDegraded', result);
        }
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      result = {
        isHealthy: false,
        responseTime,
        error: error as Error,
        timestamp: new Date(),
      };

      this.consecutiveFailures++;
      if (this.isHealthy && this.consecutiveFailures >= this.maxConsecutiveFailures) {
        this.isHealthy = false;
        console.error('❌ Database health check failed:', error);
        this.emit('healthCheckFailed', result);
      }
    }

    // Store health check result
    this.healthCheckHistory.push(result);
    if (this.healthCheckHistory.length > this.maxHistorySize) {
      this.healthCheckHistory.shift();
    }

    return result;
  }

  private collectMetrics(): void {
    // Calculate average query time
    if (this.metrics.totalQueries > 0) {
      // This would be calculated from actual query timing data
      // For now, we'll use a placeholder calculation
    }

    this.emit('metricsCollected', this.metrics);
  }

  getStats(): PoolStats {
    const pool = this.connection.getPool();
    const uptime = Date.now() - this.startTime;

    let healthStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
      healthStatus = 'unhealthy';
    } else if (this.consecutiveFailures > 0) {
      healthStatus = 'degraded';
    }

    return {
      totalConnections: pool.totalCount,
      idleConnections: pool.idleCount,
      waitingClients: pool.waitingCount,
      maxConnections: pool.options.max || 10,
      healthStatus,
      lastHealthCheck: this.healthCheckHistory.length > 0
        ? this.healthCheckHistory[this.healthCheckHistory.length - 1].timestamp
        : new Date(),
      consecutiveFailures: this.consecutiveFailures,
      uptime,
    };
  }

  getMetrics(): PoolMetrics {
    return { ...this.metrics };
  }

  getHealthHistory(): HealthCheckResult[] {
    return [...this.healthCheckHistory];
  }

  isHealthyStatus(): boolean {
    return this.isHealthy;
  }

  getHealthSummary(): {
    isHealthy: boolean;
    consecutiveFailures: number;
    lastCheck: Date | null;
    averageResponseTime: number;
    successRate: number;
  } {
    const recentChecks = this.healthCheckHistory.slice(-10); // Last 10 checks
    const successfulChecks = recentChecks.filter(check => check.isHealthy).length;
    const averageResponseTime = recentChecks.length > 0
      ? recentChecks.reduce((sum, check) => sum + check.responseTime, 0) / recentChecks.length
      : 0;
    const successRate = recentChecks.length > 0 ? successfulChecks / recentChecks.length : 0;

    return {
      isHealthy: this.isHealthy,
      consecutiveFailures: this.consecutiveFailures,
      lastCheck: this.healthCheckHistory.length > 0
        ? this.healthCheckHistory[this.healthCheckHistory.length - 1].timestamp
        : null,
      averageResponseTime,
      successRate,
    };
  }

  async executeWithRetry<T>(
    operation: (client: PoolClient) => Promise<T>,
    maxRetries = 3,
    retryDelay = 1000
  ): Promise<T> {
    let lastError: Error;
    const startTime = Date.now();

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const client = await this.connection.getClient();
        try {
          const result = await operation(client);

          // Track successful operation
          this.metrics.totalQueries++;
          this.metrics.successfulQueries++;

          const duration = Date.now() - startTime;
          if (duration > this.slowQueryThreshold) {
            this.metrics.slowQueries++;
            this.metrics.lastSlowQuery = {
              query: 'operation', // Would be actual query in real implementation
              duration,
              timestamp: new Date(),
            };
          }

          return result;
        } finally {
          client.release();
        }
      } catch (error) {
        lastError = error as Error;
        this.metrics.totalQueries++;
        this.metrics.failedQueries++;

        console.warn(`Database operation attempt ${attempt} failed:`, error);
        this.emit('operationFailed', { attempt, error, maxRetries });

        if (attempt < maxRetries && this.isRetryableError(error)) {
          await this.delay(retryDelay * attempt);
        } else {
          break;
        }
      }
    }

    throw lastError!;
  }

  private isRetryableError(error: any): boolean {
    if (!error || typeof error.code !== 'string') {
      return false;
    }

    // PostgreSQL error codes for retryable errors
    const retryableCodes = [
      '40001', // serialization_failure
      '40P01', // deadlock_detected
      '53300', // too_many_connections
      '08006', // connection_failure
      '08001', // sqlclient_unable_to_establish_sqlconnection
      '57P01', // admin_shutdown
    ];

    return retryableCodes.includes(error.code);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
